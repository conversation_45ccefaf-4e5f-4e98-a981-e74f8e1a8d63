import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import axios from 'axios';
import fs from 'fs';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Login endpoint
app.post('/login', (req, res) => {
  const { email, password } = req.body;

  // Get credentials from environment variables
  const validEmail = process.env.LOGIN_EMAIL;
  const validPassword = process.env.LOGIN_PASSWORD;

  if (!validEmail || !validPassword) {
    return res.status(500).json({
      success: false,
      message: 'Server configuration error: Missing login credentials'
    });
  }

  if (email === validEmail && password === validPassword) {
    res.json({
      success: true,
      message: 'Login successful',
      token: 'dummy-jwt-token' // In production, use proper JWT
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid email or password'
    });
  }
});

// Company profiling endpoint
app.post('/company_profiling', async (req, res) => {
  const { companyName } = req.body;
  console.log("Processing company: ", companyName);

  if (!companyName) {
    return res.status(400).json({
      success: false,
      message: 'Company name is required'
    });
  }

  const geminiApiKey = process.env.GEMINI_API_KEY;
  const modelId = process.env.MODEL_ID || 'gemini-2.5-pro';

  if (!geminiApiKey) {
    return res.status(500).json({
      success: false,
      message: 'Server configuration error: Missing Gemini API key'
    });
  }

  try {
    const systemInstruction = fs.readFileSync('./server/system_prompt.md', 'utf8');

    const requestBody = {
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `**Company to Research**: ${companyName}\n\nPlease conduct thorough research and return the information in the exact JSON format specified above.`
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.4,
        thinkingConfig: {
          thinkingBudget: 32768
        }
      },
      system_instruction: {
        parts: [
          {
            text: systemInstruction
          }
        ]
      },
      tools: [
        {
          urlContext: {}
        },
        {
          googleSearch: {}
        }
      ]
    };

    console.log("Going hit Gemini API");

    const response = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/${modelId}:generateContent?key=${geminiApiKey}`,
      requestBody,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    // Extract the generated content from Gemini response
    console.log("Raw response", JSON.stringify(response.data));
    const generatedContent = response.data.candidates?.[0]?.content?.parts?.[0]?.text;
    
    if (!generatedContent) {
      throw new Error('No content generated from Gemini API');
    }

    // Try to parse the JSON response from Gemini
    let profileData = {};
    try {
      // Extract JSON from the response (Gemini might include additional text)
      const jsonMatch = generatedContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        profileData = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No valid JSON found in response');
      }
    } catch (parseError) {
      console.error('Error parsing Gemini response:', parseError);
    }

    console.log("Profile data:", JSON.stringify(profileData));

    const reflectionPrompt = fs.readFileSync('./server/reflection_prompt.md', 'utf8');

    const reflectionRequestBody = {
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `**Company Profile**: ${JSON.stringify(profileData)}\n\nPlease review the provided company profile and enhance it as necessary.`
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.4,
        thinkingConfig: {
          thinkingBudget: 32768
        }
      },
      system_instruction: {
        parts: [
          {
            text: reflectionPrompt
          }
        ]
      },
      tools: [
        {
          urlContext: {}
        },
        {
          googleSearch: {}
        }
      ]
    };

    const reflectionResponse = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/${modelId}:generateContent?key=${geminiApiKey}`,
      reflectionRequestBody,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    const reflectionGeneratedContent = reflectionResponse.data.candidates?.[0]?.content?.parts?.[0]?.text;
    if (!reflectionGeneratedContent) {
      throw new Error('No content generated from Gemini API');
    }

    try {
      // Extract JSON from the response (Gemini might include additional text)
      const jsonMatch = reflectionGeneratedContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        profileData = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No valid JSON found in response');
      }
    } catch (parseError) {
      console.error('Error parsing Gemini reflection response:', parseError);
    }

    console.log("Final profile data:", JSON.stringify(profileData));

    res.json({
      success: true,
      data: profileData
    });

  } catch (error) {
    console.error('Error calling Gemini API:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate company profile',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
