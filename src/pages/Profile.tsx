import { useLocation, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { 
  ArrowLeft, 
  Building2, 
  Users, 
  Globe, 
  MapPin, 
  Calendar,
  TrendingUp,
  Leaf,
  Users2,
  Shield,
  Lightbulb,
  Newspaper
} from "lucide-react";
import { CompanyProfile } from "@/types/company";
import BasicInfoSection from "@/components/profile/BasicInfoSection";
import ManagementSection from "@/components/profile/ManagementSection";
import GroupStructureSection from "@/components/profile/GroupStructureSection";
import ESGSection from "@/components/profile/ESGSection";
import GrowthMetricsSection from "@/components/profile/GrowthMetricsSection";
import IndustryInsightsSection from "@/components/profile/IndustryInsightsSection";
import RecentNewsSection from "@/components/profile/RecentNewsSection";

// Sample data that matches the provided JSON structure
const sampleData: CompanyProfile = {
  company_profile: {
    basic_info: {
      company_name: "PT Rakamin Kolektif Madani",
      logo_url: "https://rakamin-lms.s3.ap-southeast-1.amazonaws.com/images/logo_rakamin-71bcd472-2d26-48e1-a544-4912cb59fb2d.png",
      industry_category: "Education Technology - Professional Training & Talent Management",
      description: "Rakamin is an end-to-end career development platform that provides vocational training and talent management solutions to bridge the skills gap between university graduates and industry needs in Indonesia. The company offers intensive bootcamps, project-based internships, and talent hiring services, focusing on high-demand digital fields like data science and digital marketing. Rakamin aims to make education more inclusive and impactful, helping both individuals achieve their career goals and companies find qualified tech talent.",
      location: "Jakarta, DKI Jakarta, Indonesia",
      employee_count: "50-100 employees",
      established_year: "Est. 2020",
      website: "https://rakamin.com/"
    },
    management_profile: [
      {
        name: "Andika Deni Prasetya",
        position: "Founder & Chief Executive Officer",
        background: "Andika is a second-time founder with prior experience at Telkom Indonesia. He founded Rakamin after identifying a significant gap between the skills of graduates and the demands of the digital industry in Indonesia."
      },
      {
        name: "Ilham Adi Pratama",
        position: "Co-Founder & Chief Technology Officer",
        background: "Ilham co-founded Rakamin alongside Andika Deni Prasetya. As CTO, he leads the technological development of Rakamin's proprietary learning and talent management platform."
      },
      {
        name: "Putra Pratama",
        position: "Chief Operating Officer",
        background: "As COO, Putra Pratama is responsible for overseeing the operational execution of Rakamin's various programs and services, ensuring efficiency and quality in their delivery."
      },
      {
        name: "Naufal Azmi Rabbani",
        position: "Head of Product",
        background: "Naufal leads the product development strategy at Rakamin, focusing on creating and enhancing the platform's features for students and corporate partners to deliver an optimal user experience."
      }
    ],
    group_structure: {
      parent_company: "Independent",
      subsidiaries: [{ name: "Rakamin Academy" }, { name: "Rakamin Career" }],
      ownership_structure: "Privately held. Received seed funding from investors including Investible, EduSpaze, and eWTP Capital."
    },
    esg_initiatives: {
      environmental: ["Information not found"],
      social: [
        "Vision is to create an inclusive and impactful education system to improve the welfare of the Indonesian people.",
        "Offers affordable and accessible training programs to bridge the talent gap and enhance career opportunities for graduates.",
        "Partners with over 150 organizations and dozens of universities to provide real-world project experience and job opportunities.",
        "Collaborates with government programs like Kominfo's Digital Talent Scholarship to upskill the national workforce."
      ],
      governance: ["Information not found"]
    },
    growth_metrics: {
      cagr: {
        percentage: 39,
        benchmark: "+39% Industry Growth",
        data_points: [
          { year: "2020", value: 10 },
          { year: "2021", value: 15 },
          { year: "2022", value: 25 },
          { year: "2023", value: 35 },
          { year: "2024", value: 50 },
          { year: "2025", value: 65 }
        ]
      }
    },
    industry_insights: [
      {
        title: "Significant Tech Talent Shortage in Indonesia",
        description: "Indonesia has the largest internet economy in Southeast Asia, but faces a significant shortage of skilled digital and technology professionals. This high demand creates a major opportunity for EdTech platforms focused on upskilling and reskilling the workforce for tech roles."
      },
      {
        title: "Growth of Alternative Education Platforms",
        description: "Traditional university programs are often insufficient to meet the specific, fast-evolving needs of the tech industry. This has led to the rise of alternative education providers, such as bootcamps and vocational training platforms, which offer more direct pathways to employment."
      },
      {
        title: "Increasing Investment in Indonesian EdTech",
        description: "Investors are showing strong interest in the Indonesian EdTech sector, recognizing its potential to solve the country's talent gap. Funding for startups in this space is aimed at scaling operations to train a larger portion of the nation's young, growing population."
      }
    ],
    recent_news: [
      {
        title: "Rakamin Collaborates with Scala by Metranet to Strengthen Business Operations Management",
        date: "04 December 2023",
        categories: ["Partnership", "Expansion"],
        summary: "Rakamin entered a collaboration with Scala by Metranet, a digitalization enabler under Telkom Group, to enhance service quality and human resource functions. The partnership aims to deliver innovative solutions for talent selection, placement, and development in Indonesia's digital industry."
      },
      {
        title: "Rakamin Secures Undisclosed Amount in Seed Funding Round",
        date: "18 May 2023",
        categories: ["Financial Results", "Growth"],
        summary: "Rakamin raised a seed funding round from three investors: Investible, EduSpaze, and eWTP Capital. The investment is intended to help the company scale its efforts in upskilling Indonesian professionals and addressing the nation's tech talent shortage."
      }
    ]
  }
};

const Profile = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [profileData, setProfileData] = useState<CompanyProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const { companyName, profileData: apiProfileData } = location.state || {};

    if (!companyName) {
      navigate("/search");
      return;
    }

    // Use API data if available, otherwise use sample data
    const loadProfile = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 500)); // Brief loading for UX
      setProfileData(apiProfileData || sampleData);
      setIsLoading(false);
    };

    loadProfile();
  }, [location.state, navigate]);

  const handleBack = () => {
    navigate("/search");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/30 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
          <p className="text-lg font-medium">Generating company profile...</p>
        </div>
      </div>
    );
  }

  if (!profileData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/30 flex items-center justify-center">
        <div className="text-center space-y-4">
          <Building2 className="w-16 h-16 text-muted-foreground mx-auto" />
          <p className="text-lg font-medium">No profile data available</p>
          <Button onClick={handleBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Search
          </Button>
        </div>
      </div>
    );
  }

  const { company_profile } = profileData;

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/30">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button variant="outline" onClick={handleBack} className="flex items-center gap-2">
            <ArrowLeft className="w-4 h-4" />
            Company Profile
          </Button>
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-success rounded-full flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full" />
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <BasicInfoSection data={company_profile.basic_info} />
          <ManagementSection data={company_profile.management_profile} />
          <GroupStructureSection data={company_profile.group_structure} />
          <ESGSection data={company_profile.esg_initiatives} />
          <GrowthMetricsSection data={company_profile.growth_metrics} />
          <IndustryInsightsSection data={company_profile.industry_insights} />
          <RecentNewsSection data={company_profile.recent_news} />
        </div>
      </div>
    </div>
  );
};

export default Profile;